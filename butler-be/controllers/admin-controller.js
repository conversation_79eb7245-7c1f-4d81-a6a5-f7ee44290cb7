import Outlet from "../models/Outlet.js";
import Category from "../models/Category.js";
import Dish from "../models/Dish.js";
import FoodChain from "../models/FoodChain.js";
import Conversation from "../models/Conversation.js";
import Order from "../models/Order.js";
import User from "../models/User.js";
import Coupon from "../models/Coupon.js";
import bcrypt from "bcrypt";
import { ObjectId } from "mongodb";
import {
  emitOrderStatusUpdate,
  emitNewOrder,
  emitPaymentUpdate,
  emitOrderItemsUpdate,
} from "../sockets/orderSocket.js";
import { reapplyCouponToOrder } from "./coupon-controller.js";
import { updatePaymentLink } from "../utils/razorpay.js";
import Payment from "../models/Payment.js";
import { processOrderInventory } from "../controllers/inventory-controller.js";

// Outlet Management
export const getAllOutlets = async (req, res) => {
  try {
    const foodChainId = req.user.foodChain;
    const outlets = await Outlet.find({ foodChain: foodChainId });

    res.status(200).json({
      success: true,
      data: outlets,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error fetching outlets",
      error: error.message,
    });
  }
};

export const createOutlet = async (req, res) => {
  try {
    const { name, address, city, pincode, contact, isCloudKitchen } = req.body;
    const foodChainId = req.user.foodChain;

    if (!name || !address || !city || !pincode) {
      return res.status(400).json({
        message: "Name, address, city, and pincode are required",
      });
    }

    const outlet = new Outlet({
      name,
      address,
      city,
      pincode,
      contact,
      foodChain: foodChainId,
      isCloudKitchen: isCloudKitchen || false,
    });

    await outlet.save();

    // Update food chain with new outlet
    await FoodChain.findByIdAndUpdate(foodChainId, {
      $push: { outlets: outlet._id },
    });

    res.status(201).json({
      success: true,
      message: "Outlet created successfully",
      data: outlet,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error creating outlet",
      error: error.message,
    });
  }
};

export const updateOutlet = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      name,
      address,
      city,
      pincode,
      contact,
      status,
      operatingHours,
      paymentSettings,
      deliveryRadius,
      deliveryZones,
      isCloudKitchen,
    } = req.body;
    const foodChainId = req.user.foodChain;

    const outlet = await Outlet.findOne({ _id: id, foodChain: foodChainId });
    if (!outlet) {
      return res.status(404).json({ message: "Outlet not found" });
    }

    // Update basic fields
    if (name) outlet.name = name;
    if (address) outlet.address = address;
    if (city) outlet.city = city;
    if (pincode) outlet.pincode = pincode;
    if (contact) outlet.contact = contact;
    if (status) outlet.status = status;
    if (isCloudKitchen !== undefined) outlet.isCloudKitchen = isCloudKitchen;
    if (deliveryRadius) outlet.deliveryRadius = deliveryRadius;
    if (deliveryZones) outlet.deliveryZones = deliveryZones;

    // Update complex objects
    if (operatingHours) outlet.operatingHours = operatingHours;
    if (paymentSettings) outlet.paymentSettings = paymentSettings;

    outlet.updatedAt = Date.now();

    await outlet.save();

    res.status(200).json({
      success: true,
      message: "Outlet updated successfully",
      data: outlet,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error updating outlet",
      error: error.message,
    });
  }
};

export const deleteOutlet = async (req, res) => {
  try {
    const { id } = req.params;
    const foodChainId = req.user.foodChain;

    const outlet = await Outlet.findOneAndDelete({
      _id: id,
      foodChain: foodChainId,
    });
    if (!outlet) {
      return res.status(404).json({ message: "Outlet not found" });
    }

    // Remove outlet from food chain
    await FoodChain.findByIdAndUpdate(foodChainId, { $pull: { outlets: id } });

    res.status(200).json({
      success: true,
      message: "Outlet deleted successfully",
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error deleting outlet",
      error: error.message,
    });
  }
};

export const getSingleOutlet = async (req, res) => {
  try {
    const { id } = req.params;
    const foodChainId = req.user.foodChain;

    const outlet = await Outlet.findOne({ _id: id, foodChain: foodChainId })
      .populate("dishes", "name price isAvailable")
      .populate("foodChain", "name")
      .populate("staff.userId", "name email phone role");
    if (!outlet) {
      return res.status(404).json({ message: "Outlet not found" });
    }

    res.status(200).json({
      success: true,
      data: outlet,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error fetching outlet",
      error: error.message,
    });
  }
};

export const updateOutletStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;
    const foodChainId = req.user.foodChain;

    // Validate status
    if (!["active", "inactive"].includes(status)) {
      return res.status(400).json({
        success: false,
        message: "Invalid status. Status must be 'active' or 'inactive'",
      });
    }

    const outlet = await Outlet.findOne({ _id: id, foodChain: foodChainId });
    if (!outlet) {
      return res.status(404).json({ message: "Outlet not found" });
    }

    outlet.status = status;
    outlet.updatedAt = Date.now();

    await outlet.save();

    res.status(200).json({
      success: true,
      message: `Outlet ${
        status === "inactive" ? "deactivated" : "activated"
      } successfully`,
      data: outlet,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error updating outlet status",
      error: error.message,
    });
  }
};

export const addStaffToOutlet = async (req, res) => {
  try {
    const { id } = req.params;
    const { userId, role } = req.body;
    const foodChainId = req.user.foodChain;

    // Validate role
    const validRoles = ["manager", "chef", "delivery", "cashier", "waiter"];
    if (!validRoles.includes(role)) {
      return res.status(400).json({
        success: false,
        message: `Invalid role. Role must be one of: ${validRoles.join(", ")}`,
      });
    }

    // Check if outlet exists
    const outlet = await Outlet.findOne({ _id: id, foodChain: foodChainId });
    if (!outlet) {
      return res.status(404).json({ message: "Outlet not found" });
    }

    // Check if user exists and belongs to the food chain
    const user = await User.findOne({ _id: userId, foodChain: foodChainId });
    if (!user) {
      return res.status(404).json({
        success: false,
        message: "User not found or doesn't belong to this food chain",
      });
    }

    // Check if user is already assigned to this outlet with this role
    const existingAssignment = outlet.staff.find(
      (staff) => staff.userId.toString() === userId && staff.role === role
    );

    if (existingAssignment) {
      return res.status(400).json({
        success: false,
        message: "User is already assigned to this outlet with this role",
      });
    }

    // Add staff to outlet
    outlet.staff.push({
      userId,
      role,
      isActive: true,
    });

    outlet.updatedAt = Date.now();
    await outlet.save();

    res.status(200).json({
      success: true,
      message: "Staff added to outlet successfully",
      data: outlet,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error adding staff to outlet",
      error: error.message,
    });
  }
};

export const removeStaffFromOutlet = async (req, res) => {
  try {
    const { id, staffId } = req.params;
    const foodChainId = req.user.foodChain;

    // Check if outlet exists
    const outlet = await Outlet.findOne({ _id: id, foodChain: foodChainId });
    if (!outlet) {
      return res.status(404).json({ message: "Outlet not found" });
    }

    // Check if staff assignment exists
    const staffIndex = outlet.staff.findIndex(
      (staff) => staff._id.toString() === staffId
    );

    if (staffIndex === -1) {
      return res.status(404).json({
        success: false,
        message: "Staff assignment not found",
      });
    }

    // Remove staff from outlet
    outlet.staff.splice(staffIndex, 1);
    outlet.updatedAt = Date.now();
    await outlet.save();

    res.status(200).json({
      success: true,
      message: "Staff removed from outlet successfully",
      data: outlet,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error removing staff from outlet",
      error: error.message,
    });
  }
};

// Category Management
export const createCategory = async (req, res) => {
  try {
    const { name, description } = req.body;
    const foodChainId = req.user.foodChain;

    if (!name) {
      return res.status(400).json({ message: "Category name is required" });
    }

    const category = new Category({
      name,
      description,
      foodChain: foodChainId,
    });

    await category.save();

    // Update food chain with new category
    await FoodChain.findByIdAndUpdate(foodChainId, {
      $push: { categories: category._id },
    });

    res.status(201).json({
      success: true,
      message: "Category created successfully",
      data: category,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error creating category",
      error: error.message,
    });
  }
};

export const updateCategory = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, description } = req.body;
    const foodChainId = req.user.foodChain;

    const category = await Category.findOne({
      _id: id,
      foodChain: foodChainId,
    });
    if (!category) {
      return res.status(404).json({ message: "Category not found" });
    }

    if (name) category.name = name;
    if (description) category.description = description;
    category.updatedAt = Date.now();

    await category.save();

    res.status(200).json({
      success: true,
      message: "Category updated successfully",
      data: category,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error updating category",
      error: error.message,
    });
  }
};

export const getAllCategories = async (req, res) => {
  try {
    const foodChainId = req.user.foodChain;
    const categories = await Category.find({ foodChain: foodChainId }).populate(
      "dishes",
      "name price isAvailable"
    );

    res.status(200).json({
      success: true,
      data: categories,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error fetching categories",
      error: error.message,
    });
  }
};

export const deleteCategory = async (req, res) => {
  try {
    const { id } = req.params;
    const foodChainId = req.user.foodChain;

    const category = await Category.findOneAndDelete({
      _id: id,
      foodChain: foodChainId,
    });
    if (!category) {
      return res.status(404).json({ message: "Category not found" });
    }

    // Remove category from food chain
    await FoodChain.findByIdAndUpdate(foodChainId, {
      $pull: { categories: id },
    });

    // Delete all dishes in this category
    await Dish.deleteMany({ category: id });

    res.status(200).json({
      success: true,
      message: "Category and associated dishes deleted successfully",
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error deleting category",
      error: error.message,
    });
  }
};

// Theme Management
export const updateTheme = async (req, res) => {
  try {
    const { theme } = req.body;
    const foodChainId = req.user.foodChain;

    const foodChain = await FoodChain.findById(foodChainId);
    if (!foodChain) {
      return res.status(404).json({ message: "Food chain not found" });
    }

    foodChain.theme = theme;
    foodChain.updatedAt = Date.now();

    await foodChain.save();

    res.status(200).json({
      success: true,
      message: "Theme updated successfully",
      data: foodChain.theme,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error updating theme",
      error: error.message,
    });
  }
};

export const updateDish = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      name,
      price,
      description,
      isAvailable,
      category,
      cuisine,
      outlets,
      image,
      timeAvailability,
      isSeasonal,
      seasonalAvailability,
      ingredients,
      isVeg,
      isFeatured,
    } = req.body;
    const foodChainId = req.user.foodChain;

    // Verify the dish belongs to the food chain
    const existingDish = await Dish.findOne({
      _id: id,
      foodChain: foodChainId,
    });

    if (!existingDish) {
      return res.status(404).json({
        success: false,
        message: "Dish not found or doesn't belong to this food chain",
      });
    }

    // Update the dish
    const updatedDish = await Dish.findByIdAndUpdate(
      id,
      {
        $set: {
          name: name || existingDish.name,
          price: price || existingDish.price,
          description: description || existingDish.description,
          isAvailable:
            isAvailable !== undefined ? isAvailable : existingDish.isAvailable,
          category: category || existingDish.category,
          updatedAt: Date.now(),
          cuisine: cuisine || existingDish.cuisine,
          outlets: outlets || existingDish.outlets,
          image: image || existingDish.image,
          timeAvailability: timeAvailability || existingDish.timeAvailability,
          isSeasonal:
            isSeasonal !== undefined ? isSeasonal : existingDish.isSeasonal,
          seasonalAvailability:
            seasonalAvailability || existingDish.seasonalAvailability,
          ingredients: ingredients || existingDish.ingredients,
          isVeg: isVeg !== undefined ? isVeg : existingDish.isVeg,
          isFeatured:
            isFeatured !== undefined ? isFeatured : existingDish.isFeatured,
        },
      },
      { new: true }
    ).populate("category", "name");

    const existingOutletIds = existingDish.outlets.map((o) => o.toString());
    const newOutletIds = outlets.map((o) => o.toString());

    const addedOutlets = newOutletIds.filter(
      (id) => !existingOutletIds.includes(id)
    );
    const removedOutlets = existingOutletIds.filter(
      (id) => !newOutletIds.includes(id)
    );

    // Remove dish from removed outlets
    if (removedOutlets.length > 0) {
      await Outlet.updateMany(
        { _id: { $in: removedOutlets } },
        { $pull: { dishes: id } }
      );
    }

    // Add dish to newly added outlets
    if (addedOutlets.length > 0) {
      await Outlet.updateMany(
        { _id: { $in: addedOutlets } },
        { $addToSet: { dishes: id } }
      );
    }

    res.status(200).json({
      success: true,
      message: "Dish updated successfully",
      data: updatedDish,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error updating dish",
      error: error.message,
    });
  }
};

export const getDish = async (req, res) => {
  try {
    const { id } = req.params;
    const foodChainId = req.user.foodChain;

    const dish = await Dish.findOne({
      _id: id,
      foodChain: foodChainId,
    }).populate("category", "name");

    if (!dish) {
      return res.status(404).json({
        success: false,
        message: "Dish not found or doesn't belong to this food chain",
      });
    }

    res.status(200).json({
      success: true,
      data: dish,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error fetching dish",
      error: error.message,
    });
  }
};

export const deleteDish = async (req, res) => {
  try {
    const { id } = req.params;
    const foodChainId = req.user.foodChain;

    const dish = await Dish.findOneAndDelete({
      _id: id,
      foodChain: foodChainId,
    });

    if (!dish) {
      return res.status(404).json({
        success: false,
        message: "Dish not found or doesn't belong to this food chain",
      });
    }

    // Remove dish from category
    await Category.findByIdAndUpdate(dish.category, {
      $pull: { dishes: id },
    });

    // Remove dish from outlet
    await Outlet.updateMany(
      { foodChain: foodChainId },
      { $pull: { dishes: id } }
    );

    res.status(200).json({
      success: true,
      message: "Dish deleted successfully",
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error deleting dish",
      error: error.message,
    });
  }
};

// Toggle featured status for a dish
export const toggleDishFeaturedStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { isFeatured } = req.body;
    const foodChainId = req.user.foodChain;

    // Verify the dish belongs to the food chain
    const existingDish = await Dish.findOne({
      _id: id,
      foodChain: foodChainId,
    });

    if (!existingDish) {
      return res.status(404).json({
        success: false,
        message: "Dish not found or doesn't belong to this food chain",
      });
    }

    // Update the featured status
    const updatedDish = await Dish.findByIdAndUpdate(
      id,
      {
        $set: {
          isFeatured:
            isFeatured !== undefined ? isFeatured : !existingDish.isFeatured,
          updatedAt: Date.now(),
        },
      },
      { new: true }
    ).populate("category", "name");

    res.status(200).json({
      success: true,
      message: `Dish ${
        updatedDish.isFeatured ? "marked as featured" : "removed from featured"
      }`,
      data: updatedDish,
    });
  } catch (error) {
    console.error("Error toggling dish featured status:", error);
    res.status(500).json({
      success: false,
      message: "Error updating dish featured status",
      error: error.message,
    });
  }
};

export const updateDishes = async (req, res) => {
  try {
    const { dishes } = req.body;
    const foodChainId = req.user.foodChain;

    if (!Array.isArray(dishes)) {
      return res.status(400).json({
        success: false,
        message: "Dishes must be an array",
      });
    }

    const updatePromises = dishes.map(async (dish) => {
      const { id, name, price, description, isAvailable, category } = dish;

      // Verify the dish belongs to the food chain
      const existingDish = await Dish.findOne({
        _id: id,
        foodChain: foodChainId,
      });

      if (!existingDish) {
        throw new Error(
          `Dish with id ${id} not found or doesn't belong to this food chain`
        );
      }

      // Update the dish
      return Dish.findByIdAndUpdate(
        id,
        {
          $set: {
            name: name || existingDish.name,
            price: price || existingDish.price,
            description: description || existingDish.description,
            isAvailable:
              isAvailable !== undefined
                ? isAvailable
                : existingDish.isAvailable,
            category: category || existingDish.category,
            updatedAt: Date.now(),
          },
        },
        { new: true }
      ).populate("category", "name");
    });

    const updatedDishes = await Promise.all(updatePromises);

    res.status(200).json({
      success: true,
      message: "Dishes updated successfully",
      data: updatedDishes,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error updating dishes",
      error: error.message,
    });
  }
};

export const createDish = async (req, res) => {
  try {
    const {
      name,
      description,
      price,
      category,
      image,
      isAvailable = true,
      outlets,
      cuisine,
    } = req.body;
    const foodChainId = req.user.foodChain;

    // Validate required fields
    if (!name || !price || !category) {
      return res.status(400).json({
        success: false,
        message: "Name, price, and category are required",
      });
    }

    // Verify the category belongs to the food chain
    const existingCategory = await Category.findOne({
      _id: category,
      foodChain: foodChainId,
    });

    if (!existingCategory) {
      return res.status(404).json({
        success: false,
        message: "Category not found or doesn't belong to this food chain",
      });
    }

    // Create new dish
    const dish = new Dish({
      name,
      description,
      price,
      category,
      image,
      isAvailable: isAvailable !== undefined ? isAvailable : true,
      outlets,
      cuisine,
      foodChain: foodChainId,
    });

    await dish.save();

    // Add dish to category
    await Category.findByIdAndUpdate(category, {
      $push: { dishes: dish._id },
    });

    // Add dish to outlet
    outlets.forEach(async (outlet) => {
      await Outlet.findByIdAndUpdate(outlet, {
        $push: { dishes: dish._id },
      });
    });

    // Populate category details in response
    const populatedDish = await Dish.findById(dish._id).populate(
      "category",
      "name"
    );

    res.status(201).json({
      success: true,
      message: "Dish created successfully",
      data: populatedDish,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error creating dish",
      error: error.message,
    });
  }
};

export const removeDishFromOutlet = async (req, res) => {
  try {
    const { dishId, outletId } = req.body;
    const foodChainId = req.user.foodChain;
    const dish = await Dish.findOne({
      _id: dishId,
      foodChain: foodChainId,
    });

    const outlet = await Outlet.findOne({
      _id: outletId,
      foodChain: foodChainId,
    });

    if (!dish) {
      return res.status(404).json({
        success: false,
        message: "Dish not found or doesn't belong to this food chain",
      });
    }

    if (!outlet) {
      return res.status(404).json({
        success: false,
        message: "Outlet not found or doesn't belong to this food chain",
      });
    }
    await Outlet.findByIdAndUpdate(outletId, {
      $pull: { dishes: dishId },
    });
    await Dish.findByIdAndUpdate(dishId, {
      $pull: { outlets: outletId },
    });

    res.status(200).json({
      success: true,
      message: "Dish removed from outlet successfully",
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error removing dish from outlet",
      error: error.message,
    });
  }
};

export const addDishToOutlet = async (req, res) => {
  try {
    const { dishId, outletId } = req.body;
    const foodChainId = req.user.foodChain;
    const dish = await Dish.findOne({
      _id: dishId,
      foodChain: foodChainId,
    });

    const outlet = await Outlet.findOne({
      _id: outletId,
      foodChain: foodChainId,
    });

    if (!dish) {
      return res.status(404).json({
        success: false,
        message: "Dish not found or doesn't belong to this food chain",
      });
    }

    if (!outlet) {
      return res.status(404).json({
        success: false,
        message: "Outlet not found or doesn't belong to this food chain",
      });
    }
    await Outlet.findByIdAndUpdate(outletId, {
      $push: { dishes: dishId },
    });
    await Dish.findByIdAndUpdate(dishId, {
      $push: { outlets: outletId },
    });

    res.status(200).json({
      success: true,
      message: "Dish added to outlet successfully",
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error adding dish to outlet",
      error: error.message,
    });
  }
};

export const getAllUserRequests = async (req, res) => {
  try {
    const { foodChainId, outletId, page = 1, limit = 30 } = req.query;

    // Convert page and limit to numbers
    const pageNum = parseInt(page, 10);
    const limitNum = parseInt(limit, 10);

    // Validate pagination parameters
    if (isNaN(pageNum) || pageNum < 1) {
      return res.status(400).json({ error: "Invalid page parameter" });
    }

    if (isNaN(limitNum) || limitNum < 1 || limitNum > 30) {
      return res
        .status(400)
        .json({ error: "Invalid limit parameter. Must be between 1 and 30." });
    }

    const filter = {};

    // Add foodChainId filter if provided (required)
    if (!foodChainId) {
      return res.status(400).json({ error: "foodChainId is required" });
    }

    filter.foodChainId = foodChainId;

    if (outletId) {
      filter.outletId = outletId;
    }

    // Get total count of conversations for pagination metadata
    const totalConversations = await Conversation.countDocuments(filter);

    // Fetch conversations with pagination
    const conversations = await Conversation.find(filter)
      .sort({ updatedAt: -1 }) // Sort by most recent
      .skip((pageNum - 1) * limitNum)
      .limit(limitNum);

    // Extract only user messages from filtered conversations
    const allUserRequests = conversations.map((conversation) => {
      return {
        userId: conversation.userId,
        conversationId: conversation.conversationId,
        outletId: conversation.outletId,
        foodChainId: conversation.foodChainId,
        requests: conversation.messages
          .filter((message) => message.sender === "user")
          .map((message) => ({
            message: message.message,
            time: message.time,
            messageId: message._id,
          })),
      };
    });

    // Calculate pagination metadata
    const totalPages = Math.ceil(totalConversations / limitNum);

    return res.status(200).json({
      data: allUserRequests,
      pagination: {
        totalItems: totalConversations,
        totalPages: totalPages,
        currentPage: pageNum,
        pageSize: limitNum,
        hasNextPage: pageNum < totalPages,
        hasPrevPage: pageNum > 1,
      },
    });
  } catch (error) {
    console.error("Error fetching filtered user requests:", error);
    return res.status(500).json({ error: "Internal server error" });
  }
};

export const getOutletOrders = async (req, res) => {
  try {
    const { outletId } = req.params;
    const { status, page = 1, limit = 20 } = req.query;
    const foodChainId = req.user.foodChain;

    const query = {
      outletId,
      foodChainId,
    };

    if (status) {
      query.status = status;
    }

    const orders = await Order.find(query)
      .sort({ createdAt: -1 })
      .skip((page - 1) * limit)
      .limit(limit)
      .populate("items.dishId", "name price")
      .populate("userId", "name phone")
      .populate("outletId", "name");

    res.status(200).json({
      success: true,
      data: orders,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error fetching outlet orders",
      error: error.message,
    });
  }
};

export const getFoodChainOrders = async (req, res) => {
  try {
    const { status, page = 1, limit = 20 } = req.query;
    const foodChainId = req.user.foodChain;

    const query = { foodChainId };
    if (status) {
      query.status = status;
    }

    const orders = await Order.find(query)
      .sort({ createdAt: -1 })
      .skip((page - 1) * limit)
      .limit(limit)
      .populate("items.dishId", "name price")
      .populate("userId", "name phone")
      .populate("outletId", "name");

    res.status(200).json({
      success: true,
      data: orders,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error fetching food chain orders",
      error: error.message,
    });
  }
};

// Get paginated admin orders with filtering
export const getPaginatedAdminOrders = async (req, res) => {
  try {
    const { status, outlet, page = 1, limit = 50 } = req.query;
    const foodChainId = req.user.foodChain;

    const pageNum = parseInt(page);
    const limitNum = Math.min(parseInt(limit), 100); // Max 100 orders per page
    const skip = (pageNum - 1) * limitNum;

    // Build query
    const query = { foodChainId };

    if (status && status !== "all") {
      query.status = status;
    }

    if (outlet && outlet !== "all") {
      query.outletId = outlet;
    }

    // Get total count for pagination
    const totalOrders = await Order.countDocuments(query);

    // Fetch orders
    const orders = await Order.find(query)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limitNum)
      .populate("items.dishId", "name price")
      .populate("userId", "name phone email")
      .populate("outletId", "name address contact");

    // Calculate pagination info
    const totalPages = Math.ceil(totalOrders / limitNum);
    const hasNext = pageNum < totalPages;
    const hasPrev = pageNum > 1;

    res.status(200).json({
      success: true,
      data: orders,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total: totalOrders,
        totalPages,
        hasNext,
        hasPrev,
      },
    });
  } catch (error) {
    console.error("Error fetching paginated admin orders:", error);
    res.status(500).json({
      success: false,
      message: "Error fetching orders",
      error: error.message,
    });
  }
};

export const updateOrderStatus = async (req, res) => {
  try {
    const { orderId } = req.params;
    const { status, cancellationReason, modificationReason } = req.body;
    const foodChainId = req.user.foodChain;

    const order = await Order.findOne({ _id: orderId, foodChainId });
    if (!order) {
      return res.status(404).json({ message: "Order not found" });
    }

    // Validate status
    const validStatuses = [
      "pending",
      "confirmed",
      "preparing",
      "ready",
      "completed",
      "cancelled",
      "rejected",
      "modified",
    ];

    if (!validStatuses.includes(status)) {
      return res.status(400).json({
        success: false,
        message: `Invalid status. Status must be one of: ${validStatuses.join(
          ", "
        )}`,
      });
    }

    // If cancelling, require a reason
    if (status === "cancelled" && !cancellationReason) {
      return res.status(400).json({
        success: false,
        message: "Cancellation reason is required when cancelling an order",
      });
    }

    // If modifying, require a reason
    if (status === "modified" && !modificationReason) {
      return res.status(400).json({
        success: false,
        message: "Modification reason is required when modifying an order",
      });
    }

    // Store previous status for inventory processing
    const previousStatus = order.status;

    // Update order
    order.status = status;
    if (cancellationReason) order.cancellationReason = cancellationReason;
    if (modificationReason) order.modificationReason = modificationReason;

    await order.save();

    // Process inventory if status changed to confirmed, completed, cancelled, or rejected
    if (
      (status === "confirmed" && previousStatus !== "confirmed") ||
      (status === "cancelled" && previousStatus !== "cancelled") ||
      (status === "rejected" && previousStatus !== "rejected")
    ) {
      // Process inventory in the background
      processOrderInventory(orderId, status, req.user._id)
        .then((result) => {
          if (!result.success) {
            console.error(
              `Error processing inventory for order ${orderId}:`,
              result.message
            );
          }
        })
        .catch((err) => {
          console.error(
            `Error processing inventory for order ${orderId}:`,
            err
          );
        });
    }

    // Emit socket event for real-time update
    await emitOrderStatusUpdate(orderId);

    res.status(200).json({
      success: true,
      message: "Order status updated successfully",
      data: order,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error updating order status",
      error: error.message,
    });
  }
};

export const updateOrderPaymentStatus = async (req, res) => {
  try {
    const { orderId } = req.params;
    const { paymentStatus, paymentMethod } = req.body;
    const foodChainId = req.user.foodChain;

    const order = await Order.findOne({ _id: orderId, foodChainId });
    if (!order) {
      return res.status(404).json({ message: "Order not found" });
    }

    order.paymentStatus = paymentStatus;
    // if (paymentStatus === "paid") {
    //   order.status = "confirmed";
    // }
    if (paymentMethod) {
      order.paymentMethod = paymentMethod;
    }
    await order.save();

    // Emit socket event for real-time payment update
    await emitPaymentUpdate(orderId);

    res.status(200).json({
      success: true,
      message: "Order payment status updated successfully",
      data: order,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error updating order payment status",
      error: error.message,
    });
  }
};

export const updateOrderPriority = async (req, res) => {
  try {
    const { orderId } = req.params;
    const { priority } = req.body;
    const foodChainId = req.user.foodChain;

    // Validate priority
    const validPriorities = ["low", "normal", "high", "urgent"];
    if (!validPriorities.includes(priority)) {
      return res.status(400).json({
        success: false,
        message: `Invalid priority. Priority must be one of: ${validPriorities.join(
          ", "
        )}`,
      });
    }

    const order = await Order.findOne({ _id: orderId, foodChainId });
    if (!order) {
      return res.status(404).json({ message: "Order not found" });
    }

    order.priority = priority;
    await order.save();

    // Emit socket event for real-time update
    await emitOrderStatusUpdate(orderId);

    res.status(200).json({
      success: true,
      message: "Order priority updated successfully",
      data: order,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error updating order priority",
      error: error.message,
    });
  }
};

export const assignOrderToStaff = async (req, res) => {
  try {
    const { orderId } = req.params;
    const { userId, role } = req.body;
    const foodChainId = req.user.foodChain;

    // Validate role
    const validRoles = ["chef", "delivery", "manager"];
    if (!validRoles.includes(role)) {
      return res.status(400).json({
        success: false,
        message: `Invalid role. Role must be one of: ${validRoles.join(", ")}`,
      });
    }

    const order = await Order.findOne({ _id: orderId, foodChainId });
    if (!order) {
      return res.status(404).json({ message: "Order not found" });
    }

    // Check if user exists and belongs to the food chain
    const user = await User.findOne({ _id: userId, foodChain: foodChainId });
    if (!user) {
      return res.status(404).json({
        success: false,
        message: "User not found or doesn't belong to this food chain",
      });
    }

    // Check if user is assigned to the outlet with the appropriate role
    const outlet = await Outlet.findById(order.outletId);
    const isAssignedToOutlet = outlet.staff.some(
      (staff) =>
        staff.userId.toString() === userId &&
        staff.role === role &&
        staff.isActive
    );

    if (!isAssignedToOutlet) {
      return res.status(400).json({
        success: false,
        message: `User is not assigned to this outlet as a ${role}`,
      });
    }

    // Assign order to staff
    order.assignedTo = {
      userId,
      role,
      assignedAt: Date.now(),
    };

    await order.save();

    // Emit socket event for real-time update
    await emitOrderStatusUpdate(orderId);

    res.status(200).json({
      success: true,
      message: "Order assigned successfully",
      data: order,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error assigning order",
      error: error.message,
    });
  }
};

export const addKitchenNotes = async (req, res) => {
  try {
    const { orderId } = req.params;
    const { kitchenNotes } = req.body;
    const foodChainId = req.user.foodChain;

    if (!kitchenNotes) {
      return res.status(400).json({
        success: false,
        message: "Kitchen notes are required",
      });
    }

    const order = await Order.findOne({ _id: orderId, foodChainId });
    if (!order) {
      return res.status(404).json({ message: "Order not found" });
    }

    order.kitchenNotes = kitchenNotes;
    await order.save();

    // Emit socket event for real-time update
    await emitOrderStatusUpdate(orderId);

    res.status(200).json({
      success: true,
      message: "Kitchen notes added successfully",
      data: order,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error adding kitchen notes",
      error: error.message,
    });
  }
};

// Admin Order Creation
export const createAdminOrder = async (req, res) => {
  try {
    const {
      outletId,
      customerId,
      items,
      specialInstructions,
      tableNumber,
      paymentMethod,
      couponCode,
      couponDiscount,
      sendInvoiceEmail,
    } = req.body;
    const foodChainId = req.user.foodChain;

    // Validate outlet
    const outlet = await Outlet.findOne({
      _id: outletId,
      foodChain: foodChainId,
    }).populate("foodChain");
    if (!outlet) {
      return res.status(404).json({ message: "Outlet not found" });
    }

    // Validate customer
    const customer = await User.findById(customerId);
    if (!customer) {
      return res.status(404).json({ message: "Customer not found" });
    }

    // Calculate total amount and validate items
    let totalAmount = 0;
    const validatedItems = [];

    for (const item of items) {
      const dish = await Dish.findById(item.dishId);
      if (!dish) {
        return res.status(400).json({
          message: `Dish ${item.dishId} not found`,
        });
      }

      validatedItems.push({
        dishId: dish._id,
        dishName: dish.name, // Store dish name for deleted dish handling
        quantity: item.quantity,
        price: dish.price,
      });

      totalAmount += dish.price * item.quantity;
    }

    // Order number will be generated by the pre-save hook

    // Create order object
    const orderData = {
      userId: customerId,
      outletId,
      foodChainId,
      items: validatedItems,
      totalAmount,
      specialInstructions,
      tableNumber,
      paymentMethod: paymentMethod || "cash",
      status: "preparing", // Admin-created orders start as confirmed
    };

    // Apply coupon if provided
    if (couponCode && couponDiscount) {
      // Validate coupon exists
      const coupon = await Coupon.findOne({
        code: couponCode.toUpperCase(),
        foodChainId,
        isActive: true,
      });

      if (coupon) {
        orderData.couponCode = couponCode.toUpperCase();
        orderData.couponDiscount = couponDiscount;
        orderData.finalAmount = totalAmount - couponDiscount;

        // Increment coupon usage count
        coupon.usedCount += 1;
        await coupon.save();
      }
    }

    // Create and save the order
    const order = new Order(orderData);
    await order.save();

    // Process inventory for the confirmed order
    processOrderInventory(order._id, "confirmed", req.user._id)
      .then((result) => {
        if (!result.success) {
          console.error(
            `Error processing inventory for order ${order._id}:`,
            result.message
          );
        }
      })
      .catch((err) => {
        console.error(
          `Error processing inventory for order ${order._id}:`,
          err
        );
      });

    // Emit socket event for real-time notification
    await emitNewOrder(order._id);

    // Send invoice email if requested
    if (sendInvoiceEmail) {
      try {
        // Import email service
        const { sendOrderInvoiceEmail } = await import(
          "../utils/emailService.js"
        );

        // Populate order items for the email
        const populatedOrder = await Order.findById(order._id).populate(
          "items.dishId"
        );

        // Send the invoice email
        await sendOrderInvoiceEmail(
          populatedOrder,
          customer,
          outlet,
          outlet.foodChain
        );
      } catch (emailError) {
        console.error("Error sending invoice email:", emailError);
        // Continue with order creation even if email fails
      }
    }

    res.status(201).json({
      success: true,
      message: "Order created successfully",
      data: order,
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({
      success: false,
      message: "Error creating order",
      error: error.message,
    });
  }
};

// Send invoice email for an order
export const sendOrderInvoice = async (req, res) => {
  try {
    const { orderId } = req.params;
    const foodChainId = req.user.foodChain;

    // Find the order
    const order = await Order.findOne({
      _id: orderId,
      foodChainId,
    }).populate("items.dishId");

    if (!order) {
      return res.status(404).json({
        success: false,
        message: "Order not found",
      });
    }

    // Get customer, outlet, and food chain details
    const [customer, outlet] = await Promise.all([
      User.findById(order.userId),
      Outlet.findById(order.outletId).populate("foodChain"),
    ]);

    if (!customer) {
      return res.status(404).json({
        success: false,
        message: "Customer not found",
      });
    }

    if (!outlet) {
      return res.status(404).json({
        success: false,
        message: "Outlet not found",
      });
    }

    // Import email service
    const { sendOrderInvoiceEmail } = await import("../utils/emailService.js");

    // Send the invoice email
    const emailSent = await sendOrderInvoiceEmail(
      order,
      customer,
      outlet,
      outlet.foodChain
    );

    if (emailSent) {
      res.status(200).json({
        success: true,
        message: "Invoice email sent successfully",
      });
    } else {
      res.status(500).json({
        success: false,
        message: "Failed to send invoice email",
      });
    }
  } catch (error) {
    console.error("Error sending invoice email:", error);
    res.status(500).json({
      success: false,
      message: "Error sending invoice email",
      error: error.message,
    });
  }
};

// Delete order (admin privilege)
export const deleteOrder = async (req, res) => {
  try {
    const { orderId } = req.params;
    const foodChainId = req.user.foodChain;

    // Find the order
    const order = await Order.findOne({ _id: orderId, foodChainId });

    if (!order) {
      return res.status(404).json({
        success: false,
        message: "Order not found",
      });
    }

    // Check if inventory was deducted for this order
    if (
      order.status === "confirmed" ||
      order.status === "preparing" ||
      order.status === "ready" ||
      order.status === "completed"
    ) {
      // Restore inventory before deleting
      await processOrderInventory(orderId, "cancelled", req.user._id);
    }

    // Delete the order
    await Order.findByIdAndDelete(orderId);

    res.status(200).json({
      success: true,
      message: "Order deleted successfully",
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error deleting order",
      error: error.message,
    });
  }
};

export const getOrderDetails = async (req, res) => {
  try {
    const { orderId } = req.params;
    const foodChainId = req.user.foodChain;

    const order = await Order.findOne({ _id: orderId, foodChainId })
      .populate("items.dishId", "name price description")
      .populate("userId", "name phone email")
      .populate("outletId", "name address contact");

    if (!order) {
      return res.status(404).json({ message: "Order not found" });
    }

    res.status(200).json({
      success: true,
      data: order,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error fetching order details",
      error: error.message,
    });
  }
};

export const getDashboardAnalytics = async (req, res) => {
  const { daysToAnalyze } = req.query;
  try {
    const foodChainId = req.user.foodChain;
    const foodChainData = await FoodChain.findById(foodChainId).select(
      "name createdAt"
    );
    const today = new Date();
    const days = daysToAnalyze ? parseInt(daysToAnalyze) : 30;
    const daysAgo = new Date(today.setDate(today.getDate() - days));

    // Add daily revenue trend
    const dailyRevenue = await Order.aggregate([
      {
        $match: {
          foodChainId,
          createdAt: { $gte: daysAgo },
          status: "completed",
        },
      },
      {
        $group: {
          _id: {
            $dateToString: { format: "%Y-%m-%d", date: "$createdAt" },
          },
          revenue: { $sum: "$totalAmount" },
        },
      },
      { $sort: { _id: 1 } },
    ]);

    // Add top selling items
    const topItems = await Order.aggregate([
      {
        $match: {
          foodChainId,
          createdAt: { $gte: daysAgo },
          status: "completed",
        },
      },
      { $unwind: "$items" },
      {
        $group: {
          _id: "$items.dishId",
          totalQuantity: { $sum: "$items.quantity" },
          totalRevenue: {
            $sum: { $multiply: ["$items.price", "$items.quantity"] },
          },
        },
      },
      { $sort: { totalQuantity: -1 } },
      { $limit: 10 },
      {
        $lookup: {
          from: "dishes",
          localField: "_id",
          foreignField: "_id",
          as: "dishDetails",
        },
      },
    ]);
    const foodChainIdStr =
      typeof foodChainId === "object" ? foodChainId.toString() : foodChainId;

    const outletPerformance = await Order.aggregate([
      {
        $match: {
          createdAt: { $gte: daysAgo },
          status: "completed",
        },
      },
      {
        $group: {
          _id: "$outletId",
          totalRevenue: { $sum: "$finalAmount" }, // Use finalAmount for accurate revenue
          totalOrders: { $sum: 1 },
          averageOrderValue: { $avg: "$finalAmount" }, // Use finalAmount for accurate average
        },
      },
      {
        $lookup: {
          from: "outlets",
          localField: "_id",
          foreignField: "_id",
          as: "outletDetails",
        },
      },
      // Unwind outletDetails to access the foodChain field
      { $unwind: "$outletDetails" },

      // Filter using a more robust comparison method
      {
        $addFields: {
          // Convert foodChain to string regardless of its current type
          foodChainStr: {
            $convert: {
              input: "$outletDetails.foodChain",
              to: "string",
            },
          },
          // Create display name with address for better identification
          displayName: {
            $concat: [
              "$outletDetails.name",
              " - ",
              {
                $arrayElemAt: [{ $split: ["$outletDetails.address", ","] }, 0],
              },
            ],
          },
        },
      },
      {
        $match: {
          foodChainStr: foodChainIdStr,
        },
      },

      // Re-wrap the outlet details as an array
      {
        $group: {
          _id: "$_id",
          totalRevenue: { $first: "$totalRevenue" },
          totalOrders: { $first: "$totalOrders" },
          averageOrderValue: { $first: "$averageOrderValue" },
          outletDetails: { $push: "$outletDetails" },
        },
      },

      { $sort: { totalRevenue: -1 } },
    ]);

    // Previous period for comparison
    const sixtyDaysAgo = new Date(today.setDate(today.getDate() - 60));

    const validOutletIds = await getValidOutletIdsForFoodChain(foodChainId);
    const [
      currentPeriodStats,
      previousPeriodStats,
      peakHours,
      customerRetention,
    ] = await Promise.all([
      // Current period stats (last 30 days)
      Order.aggregate([
        {
          $match: {
            outletId: { $in: validOutletIds }, // Use outlet IDs instead of foodChainId
            createdAt: { $gte: daysAgo },
            status: "completed",
          },
        },
        {
          $group: {
            _id: null,
            totalOrders: { $sum: 1 },
            totalRevenue: { $sum: "$totalAmount" },
            averageOrderValue: { $avg: "$totalAmount" },
            uniqueCustomers: { $addToSet: "$userId" },
          },
        },
      ]),
      Order.aggregate([
        {
          $match: {
            outletId: { $in: validOutletIds }, // Use outlet IDs instead of foodChainId
            createdAt: { $gte: sixtyDaysAgo, $lt: daysAgo },
            status: "completed",
          },
        },
        {
          $group: {
            _id: null,
            totalOrders: { $sum: 1 },
            totalRevenue: { $sum: "$totalAmount" },
          },
        },
      ]),
      Order.aggregate([
        {
          $match: {
            outletId: { $in: validOutletIds }, // Use outlet IDs instead of foodChainId
            createdAt: { $gte: daysAgo },
            status: "completed",
          },
        },
        {
          $group: {
            _id: { $hour: "$createdAt" },
            orderCount: { $sum: 1 },
            revenue: { $sum: "$finalAmount" }, // Use finalAmount instead of totalAmount for accurate revenue
          },
        },
        { $sort: { _id: 1 } }, // Sort by hour instead of orderCount for chronological order
      ]),

      // Customer frequency analysis - how many orders each customer has made
      Order.aggregate([
        {
          $match: {
            outletId: { $in: validOutletIds },
            createdAt: { $gte: daysAgo },
            status: "completed",
          },
        },
        {
          $group: {
            _id: "$userId", // Group by customer
            orderCount: { $sum: 1 }, // Count orders per customer
          },
        },
        {
          $group: {
            _id: "$orderCount", // Group by number of orders
            customerCount: { $sum: 1 }, // Count customers with this many orders
          },
        },
        {
          $sort: { _id: 1 }, // Sort by order count
        },
      ]),
    ]);

    const fillMissingHours = (peakHoursData) => {
      const result = [];

      // Create a map of existing data
      const dataMap = {};
      peakHoursData.forEach((item) => {
        dataMap[item._id] = item;
      });

      // Fill in all 24 hours (or 12 hours if using 12-hour format)
      for (let hour = 0; hour < 24; hour++) {
        if (dataMap[hour]) {
          result.push(dataMap[hour]);
        } else {
          result.push({
            _id: hour,
            orderCount: 0,
            revenue: 0,
          });
        }
      }

      return result;
    };

    // Use this function to process the MongoDB results

    const completeHoursData = fillMissingHours(peakHours);

    // Popular combinations (items frequently ordered together)
    const popularCombinations = await Order.aggregate([
      {
        $match: {
          foodChainId,
          createdAt: { $gte: daysAgo },
          status: "completed",
        },
      },
      { $unwind: "$items" },
      {
        $group: {
          _id: {
            orderId: "$_id",
            dishId: "$items.dishId",
          },
          qty: { $first: "$items.quantity" },
        },
      },
      {
        $group: {
          _id: "$_id.orderId",
          items: {
            $push: {
              dishId: "$_id.dishId",
              qty: "$qty",
            },
          },
        },
      },
      { $unwind: "$items" },
      { $unwind: "$items" },
      {
        $group: {
          _id: {
            dish1: "$items.dishId",
            dish2: "$items.dishId",
          },
          count: { $sum: 1 },
        },
      },
      { $match: { "_id.dish1": { $ne: "$_id.dish2" } } },
      { $sort: { count: -1 } },
      { $limit: 5 },
      {
        $lookup: {
          from: "dishes",
          localField: "_id.dish1",
          foreignField: "_id",
          as: "dish1Details",
        },
      },
      {
        $lookup: {
          from: "dishes",
          localField: "_id.dish2",
          foreignField: "_id",
          as: "dish2Details",
        },
      },
    ]);

    res.status(200).json({
      success: true,
      data: {
        summary: {
          ...currentPeriodStats[0],
          uniqueCustomers: currentPeriodStats[0]?.uniqueCustomers?.length || 0,
          growth: {
            revenue:
              (((currentPeriodStats[0]?.totalRevenue || 0) -
                (previousPeriodStats[0]?.totalRevenue || 0)) /
                (previousPeriodStats[0]?.totalRevenue || 1)) *
              100,
            orders:
              (((currentPeriodStats[0]?.totalOrders || 0) -
                (previousPeriodStats[0]?.totalOrders || 0)) /
                (previousPeriodStats[0]?.totalOrders || 1)) *
              100,
            averageOrderValue:
              (((currentPeriodStats[0]?.averageOrderValue || 0) -
                (previousPeriodStats[0]?.averageOrderValue || 0)) /
                (previousPeriodStats[0]?.averageOrderValue || 1)) *
              100,
            uniqueCustomers: 0, // Calculate if needed
          },
        },
        dailyRevenue,
        topItems,
        outletPerformance,
        peakHours: completeHoursData,
        customerRetention,
        popularCombinations,
        foodChainData,
      },
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error fetching analytics",
      error: error.message,
    });
  }
};

export const getCustomers = async (req, res) => {
  try {
    const { page = 1, limit = 20, search = "" } = req.query;

    // Convert page and limit to numbers
    const pageNum = parseInt(page, 10);
    const limitNum = parseInt(limit, 10);
    const skip = (pageNum - 1) * limitNum;

    // Build query to get all users with role "user"
    let query = { role: "user" };

    // Add search filter if provided
    if (search) {
      const searchRegex = new RegExp(search, "i");
      query.$or = [
        { name: searchRegex },
        { email: searchRegex },
        { phone: searchRegex },
      ];
    }

    // Get total count for pagination
    const totalCustomers = await User.countDocuments(query);

    // Get customers with pagination
    const customers = await User.find(query)
      .select("_id name email phone status createdAt")
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limitNum);

    res.status(200).json({
      success: true,
      data: {
        customers,
        pagination: {
          total: totalCustomers,
          page: pageNum,
          limit: limitNum,
          pages: Math.ceil(totalCustomers / limitNum),
        },
      },
    });
  } catch (error) {
    console.error("Error in getCustomers:", error);
    res.status(500).json({
      success: false,
      message: "Error fetching customers",
      error: error.message,
    });
  }
};

// Admin Profile Management
export const getAdminProfile = async (req, res) => {
  try {
    const userId = req.user.userId;

    // Find the admin user
    const admin = await User.findById(userId)
      .select("-password")
      .populate("foodChain", "name");

    if (!admin) {
      return res.status(404).json({
        success: false,
        message: "Admin not found",
      });
    }

    res.status(200).json({
      success: true,
      data: admin,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error fetching admin profile",
      error: error.message,
    });
  }
};

export const updateAdminProfile = async (req, res) => {
  try {
    const userId = req.user.userId;
    const { name, phone } = req.body;

    // Find the admin user
    const admin = await User.findById(userId);

    if (!admin) {
      return res.status(404).json({
        success: false,
        message: "Admin not found",
      });
    }

    // Update fields
    if (name) admin.name = name;
    if (phone) admin.phone = phone;
    admin.updatedAt = Date.now();

    await admin.save();

    res.status(200).json({
      success: true,
      message: "Admin profile updated successfully",
      data: admin,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error updating admin profile",
      error: error.message,
    });
  }
};

export const changeAdminPassword = async (req, res) => {
  try {
    const userId = req.user.userId;
    const { oldPassword, newPassword, isFirstLogin } = req.body;

    // Validate input
    if (!newPassword) {
      return res.status(400).json({
        success: false,
        message: "New password is required",
      });
    }

    // Find the admin user
    const admin = await User.findById(userId);

    if (!admin) {
      return res.status(404).json({
        success: false,
        message: "Admin not found",
      });
    }

    // If it's not a first login, verify the old password
    if (!isFirstLogin) {
      if (!oldPassword) {
        return res.status(400).json({
          success: false,
          message: "Old password is required",
        });
      }

      const isPasswordValid = await bcrypt.compare(oldPassword, admin.password);
      if (!isPasswordValid) {
        return res.status(401).json({
          success: false,
          message: "Current password is incorrect",
        });
      }

      // Check if new password is same as old password
      const isSamePassword = await bcrypt.compare(newPassword, admin.password);
      if (isSamePassword) {
        return res.status(400).json({
          success: false,
          message: "New password cannot be the same as the current password",
        });
      }
    }

    // Hash new password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(newPassword, salt);

    // Update password
    admin.password = hashedPassword;
    admin.updatedAt = Date.now();

    // If this was a first login, clear the flag
    if (admin.isFirstLogin) {
      admin.isFirstLogin = false;
    }

    await admin.save();

    res.status(200).json({
      success: true,
      message: "Password updated successfully",
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error updating password",
      error: error.message,
    });
  }
};

// Admin Management
export const getAllAdmins = async (req, res) => {
  try {
    const foodChainId = req.user.foodChain;

    // Find all admins for the food chain
    const admins = await User.find({
      foodChain: foodChainId,
      role: "admin",
    }).select("-password");

    res.status(200).json({
      success: true,
      data: admins,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error fetching admins",
      error: error.message,
    });
  }
};

// Employee Management
export const getAllEmployees = async (req, res) => {
  try {
    const foodChainId = req.user.foodChain;
    const { page = 1, limit = 20, search = "" } = req.query;

    // Convert page and limit to numbers
    const pageNum = parseInt(page, 10);
    const limitNum = parseInt(limit, 10);
    const skip = (pageNum - 1) * limitNum;

    // Build query to get all employees
    let query = {
      foodChain: foodChainId,
      role: { $nin: ["user", "super_admin"] }, // Exclude regular users and super admins
    };

    // Add search filter if provided
    if (search) {
      const searchRegex = new RegExp(search, "i");
      query.$or = [
        { name: searchRegex },
        { email: searchRegex },
        { phone: searchRegex },
      ];
    }

    // Get total count for pagination
    const totalEmployees = await User.countDocuments(query);

    // Get employees with pagination
    const employees = await User.find(query)
      .select("_id name email phone role status createdAt")
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limitNum);

    // Get outlet assignments for each employee
    const employeesWithOutlets = await Promise.all(
      employees.map(async (employee) => {
        const outlets = await Outlet.find({
          foodChain: foodChainId,
          "staff.userId": employee._id,
        }).select("name _id");

        return {
          ...employee.toObject(),
          outlets: outlets.map((outlet) => ({
            _id: outlet._id,
            name: outlet.name,
          })),
        };
      })
    );

    res.status(200).json({
      success: true,
      data: {
        employees: employeesWithOutlets,
        pagination: {
          total: totalEmployees,
          page: pageNum,
          limit: limitNum,
          pages: Math.ceil(totalEmployees / limitNum),
        },
      },
    });
  } catch (error) {
    console.error("Error in getAllEmployees:", error);
    res.status(500).json({
      success: false,
      message: "Error fetching employees",
      error: error.message,
    });
  }
};

export const createEmployee = async (req, res) => {
  try {
    const { name, email, phone, role, outletIds = [] } = req.body;
    const foodChainId = req.user.foodChain;

    // Validate required fields
    if (!name || !email || !role) {
      return res.status(400).json({
        success: false,
        message: "Name, email, and role are required",
      });
    }

    // Validate role
    const validRoles = [
      "admin",
      "manager",
      "chef",
      "delivery",
      "cashier",
      "waiter",
    ];
    if (!validRoles.includes(role)) {
      return res.status(400).json({
        success: false,
        message: `Invalid role. Role must be one of: ${validRoles.join(", ")}`,
      });
    }

    // Check if user already exists
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: "User with this email already exists",
      });
    }

    // Generate a random password (user can reset it later)
    const randomPassword = Math.random().toString(36).slice(-8);
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(randomPassword, salt);

    // Create new employee
    const employee = new User({
      email,
      password: hashedPassword,
      name,
      phone: phone || "",
      role,
      foodChain: foodChainId,
      isFirstLogin: true,
      createdBy: "admin",
    });

    await employee.save();

    // Add employee to selected outlets
    if (outletIds.length > 0) {
      for (const outletId of outletIds) {
        const outlet = await Outlet.findOne({
          _id: outletId,
          foodChain: foodChainId,
        });

        if (outlet) {
          outlet.staff.push({
            userId: employee._id,
            role: role === "admin" ? "manager" : role, // If admin, assign as manager in outlet
            isActive: true,
          });
          await outlet.save();
        }
      }
    }

    // Get food chain details for the welcome email
    const foodChain = await FoodChain.findById(foodChainId).select("name");
    const foodChainName = foodChain ? foodChain.name : "our restaurant";

    // Send welcome email with login instructions
    try {
      const { sendEmployeeWelcomeEmail } = await import(
        "../utils/emailService.js"
      );
      await sendEmployeeWelcomeEmail(
        { name, email, role },
        randomPassword,
        foodChainName
      );
    } catch (emailError) {
      console.error("Error sending employee welcome email:", emailError);
      // Continue with the response even if email fails
    }

    res.status(201).json({
      success: true,
      message: "Employee created successfully",
      data: {
        _id: employee._id,
        name: employee.name,
        email: employee.email,
        phone: employee.phone,
        role: employee.role,
        initialPassword: randomPassword, // Include the initial password in the response
      },
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error creating employee",
      error: error.message,
    });
  }
};

export const updateEmployee = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, phone, email, role, status, outletIds = [] } = req.body;
    const foodChainId = req.user.foodChain;

    // Find the employee
    const employee = await User.findOne({
      _id: id,
      foodChain: foodChainId,
      role: { $nin: ["user", "super_admin"] },
    });

    if (!employee) {
      return res.status(404).json({
        success: false,
        message: "Employee not found",
      });
    }

    // Validate role if provided
    if (role) {
      const validRoles = [
        "admin",
        "manager",
        "chef",
        "delivery",
        "cashier",
        "waiter",
      ];
      if (!validRoles.includes(role)) {
        return res.status(400).json({
          success: false,
          message: `Invalid role. Role must be one of: ${validRoles.join(
            ", "
          )}`,
        });
      }
    }

    // Update fields
    if (name) employee.name = name;
    if (phone) employee.phone = phone;
    if (email && email !== employee.email) {
      // Check if email is already in use
      const existingUser = await User.findOne({ email, _id: { $ne: id } });
      if (existingUser) {
        return res.status(400).json({
          success: false,
          message: "Email is already in use by another user",
        });
      }
      employee.email = email;
    }
    if (role) employee.role = role;
    if (status && ["active", "blocked"].includes(status)) {
      employee.status = status;
    }

    employee.updatedAt = Date.now();
    await employee.save();

    // Update outlet assignments if provided
    if (outletIds) {
      // Get all outlets for this food chain
      const outlets = await Outlet.find({ foodChain: foodChainId });

      // Remove employee from all outlets
      for (const outlet of outlets) {
        const staffIndex = outlet.staff.findIndex(
          (staff) => staff.userId.toString() === id
        );

        if (staffIndex !== -1) {
          outlet.staff.splice(staffIndex, 1);
          await outlet.save();
        }
      }

      // Add employee to selected outlets
      for (const outletId of outletIds) {
        const outlet = await Outlet.findOne({
          _id: outletId,
          foodChain: foodChainId,
        });

        if (outlet) {
          outlet.staff.push({
            userId: employee._id,
            role: employee.role === "admin" ? "manager" : employee.role,
            isActive: true,
          });
          await outlet.save();
        }
      }
    }

    res.status(200).json({
      success: true,
      message: "Employee updated successfully",
      data: employee,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error updating employee",
      error: error.message,
    });
  }
};

export const deleteEmployee = async (req, res) => {
  try {
    const { id } = req.params;
    const foodChainId = req.user.foodChain;

    // Find the employee
    const employee = await User.findOne({
      _id: id,
      foodChain: foodChainId,
      role: { $nin: ["user", "super_admin"] },
    });

    if (!employee) {
      return res.status(404).json({
        success: false,
        message: "Employee not found",
      });
    }

    // Remove employee from all outlets
    const outlets = await Outlet.find({
      foodChain: foodChainId,
      "staff.userId": employee._id,
    });

    for (const outlet of outlets) {
      outlet.staff = outlet.staff.filter(
        (staff) => staff.userId.toString() !== id
      );
      await outlet.save();
    }

    // Delete the employee
    await User.findByIdAndDelete(id);

    res.status(200).json({
      success: true,
      message: "Employee deleted successfully",
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error deleting employee",
      error: error.message,
    });
  }
};

export const resetEmployeePassword = async (req, res) => {
  try {
    const { id } = req.params;
    const foodChainId = req.user.foodChain;

    // Find the employee
    const employee = await User.findOne({
      _id: id,
      foodChain: foodChainId,
      role: { $nin: ["user", "super_admin"] },
    });

    if (!employee) {
      return res.status(404).json({
        success: false,
        message: "Employee not found",
      });
    }

    // Generate a new random password
    const newPassword = Math.random().toString(36).slice(-8);
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(newPassword, salt);

    // Update password
    employee.password = hashedPassword;
    employee.updatedAt = Date.now();
    await employee.save();

    // Get food chain details for the email
    const foodChain = await FoodChain.findById(foodChainId).select("name");
    const foodChainName = foodChain ? foodChain.name : "our restaurant";

    // Send password reset email
    try {
      const { sendEmployeeWelcomeEmail } = await import(
        "../utils/emailService.js"
      );
      await sendEmployeeWelcomeEmail(
        {
          name: employee.name,
          email: employee.email,
          role: employee.role,
        },
        newPassword,
        foodChainName
      );
    } catch (emailError) {
      console.error("Error sending employee password reset email:", emailError);
      // Continue with the response even if email fails
    }

    res.status(200).json({
      success: true,
      message: "Password reset successfully",
      data: {
        newPassword,
      },
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error resetting password",
      error: error.message,
    });
  }
};

export const resetCustomerPassword = async (req, res) => {
  try {
    const { id } = req.params;
    const foodChainId = req.user.foodChain;

    // Find the customer
    const customer = await User.findOne({
      _id: id,
      role: "user",
    });

    if (!customer) {
      return res.status(404).json({
        success: false,
        message: "Customer not found",
      });
    }

    // Generate a new random password
    const newPassword = Math.random().toString(36).slice(-8);
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(newPassword, salt);

    // Update password
    customer.password = hashedPassword;
    customer.updatedAt = Date.now();
    await customer.save();

    // Get food chain details for the email
    const foodChain = await FoodChain.findById(foodChainId).select("name");
    const foodChainName = foodChain ? foodChain.name : "our restaurant";

    // Send password reset email
    try {
      const { sendCustomerWelcomeEmail } = await import(
        "../utils/emailService.js"
      );
      await sendCustomerWelcomeEmail(
        {
          name: customer.name,
          email: customer.email,
        },
        newPassword,
        foodChainName
      );
    } catch (emailError) {
      console.error("Error sending customer password reset email:", emailError);
      // Continue with the response even if email fails
    }

    res.status(200).json({
      success: true,
      message: "Password reset successfully",
      data: {
        password: newPassword,
      },
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error resetting password",
      error: error.message,
    });
  }
};

// Customer Management
export const getCustomerDetails = async (req, res) => {
  try {
    const { id } = req.params;

    // Find the customer
    const customer = await User.findOne({
      _id: id,
      role: "user",
    }).select("-password");

    if (!customer) {
      return res.status(404).json({
        success: false,
        message: "Customer not found",
      });
    }

    // Get customer orders
    const orders = await Order.find({ userId: id })
      .sort({ createdAt: -1 })
      .limit(10);

    res.status(200).json({
      success: true,
      data: {
        customer,
        recentOrders: orders,
      },
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error fetching customer details",
      error: error.message,
    });
  }
};

export const updateCustomerDetails = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, phone, email, address } = req.body;

    // Find the customer
    const customer = await User.findOne({
      _id: id,
      role: "user",
    });

    if (!customer) {
      return res.status(404).json({
        success: false,
        message: "Customer not found",
      });
    }

    // Update fields
    if (name) customer.name = name;
    if (phone) customer.phone = phone;
    if (email) customer.email = email;
    if (address) customer.address = address;
    customer.updatedAt = Date.now();

    await customer.save();

    res.status(200).json({
      success: true,
      message: "Customer details updated successfully",
      data: customer,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error updating customer details",
      error: error.message,
    });
  }
};

export const createCustomer = async (req, res) => {
  try {
    const { name, email, phone } = req.body;
    const foodChainId = req.user.foodChain;

    if (!name || !email) {
      return res.status(400).json({
        success: false,
        message: "Name and email are required",
      });
    }

    // Check if user already exists
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: "User with this email already exists",
      });
    }

    // Generate a random password (user can reset it later)
    const randomPassword = Math.random().toString(36).slice(-8);
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(randomPassword, salt);

    // Create new user
    const user = new User({
      email,
      password: hashedPassword,
      name,
      phone: phone || "",
      role: "user",
      isFirstLogin: true,
      createdBy: "admin",
    });

    await user.save();

    // Get food chain details for the welcome email
    const foodChain = await FoodChain.findById(foodChainId).select("name");
    const foodChainName = foodChain ? foodChain.name : "our restaurant";

    // Send welcome email with login instructions
    try {
      const { sendCustomerWelcomeEmail } = await import(
        "../utils/emailService.js"
      );
      await sendCustomerWelcomeEmail(
        { name, email },
        randomPassword,
        foodChainName
      );
    } catch (emailError) {
      console.error("Error sending welcome email:", emailError);
      // Continue with the response even if email fails
    }

    res.status(201).json({
      success: true,
      message: "Customer created successfully",
      data: {
        _id: user._id,
        name: user.name,
        email: user.email,
        phone: user.phone,
        role: user.role,
        initialPassword: randomPassword, // Include the initial password in the response
      },
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error creating customer",
      error: error.message,
    });
  }
};

export const updateCustomerStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;

    // Validate status
    const validStatuses = ["active", "blocked"];
    if (!validStatuses.includes(status)) {
      return res.status(400).json({
        success: false,
        message: "Invalid status. Status must be 'active' or 'blocked'",
      });
    }

    // Find the customer
    const customer = await User.findOne({
      _id: id,
      role: "user",
    });

    if (!customer) {
      return res.status(404).json({
        success: false,
        message: "Customer not found",
      });
    }

    // Update status
    customer.status = status;
    customer.updatedAt = Date.now();

    await customer.save();

    res.status(200).json({
      success: true,
      message: `Customer ${
        status === "blocked" ? "blocked" : "activated"
      } successfully`,
      data: customer,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error updating customer status",
      error: error.message,
    });
  }
};

export const getValidOutletIdsForFoodChain = async (foodChainId) => {
  // Ensure consistent string format for comparison
  const foodChainIdStr =
    typeof foodChainId === "object" ? foodChainId.toString() : foodChainId;

  // Get all outlets that belong to this food chain
  const validOutlets = await Outlet.find({
    foodChain: {
      $in: [
        foodChainIdStr,
        new ObjectId(foodChainIdStr), // Try both string and ObjectId
      ],
    },
  }).select("_id");

  // Extract just the IDs into an array
  return validOutlets.map((outlet) => outlet._id);
};

export const markDishAsServed = async (req, res) => {
  try {
    const { orderId, itemIndex } = req.params;
    const { isServed } = req.body;
    const foodChainId = req.user.foodChain;
    const adminId = req.user._id;

    const order = await Order.findOne({ _id: orderId, foodChainId });
    if (!order) {
      return res.status(404).json({
        success: false,
        message: "Order not found",
      });
    }

    // Validate item index
    if (itemIndex < 0 || itemIndex >= order.items.length) {
      return res.status(400).json({
        success: false,
        message: "Invalid item index",
      });
    }

    // Update the specific item's served status
    const item = order.items[itemIndex];

    if (isServed) {
      // Mark entire quantity as served
      item.servedQuantity = item.quantity;
      item.isServed = true;
      item.servedAt = new Date();
      item.servedBy = adminId;
    } else {
      // Mark as not served
      item.servedQuantity = 0;
      item.isServed = false;
      item.servedAt = undefined;
      item.servedBy = undefined;
    }

    // Note: Removed auto-completion logic - admin should manually mark order as completed
    // This allows for additional items to be added or bill collection to be handled manually

    order.updatedAt = new Date();
    await order.save();

    // Emit socket event for real-time update
    await emitOrderStatusUpdate(orderId);

    res.status(200).json({
      success: true,
      message: `Dish ${
        isServed ? "marked as served" : "unmarked as served"
      } successfully`,
      data: order,
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({
      success: false,
      message: "Error updating dish serving status",
      error: error.message,
    });
  }
};

// Rate limiting for admin order updates (shared with user updates)
const adminOrderUpdateLocks = new Map();

export const updateAdminOrderItems = async (req, res) => {
  const { orderId } = req.params; // Move outside try block for error handling

  try {
    const { items, couponCode, couponDiscount } = req.body;
    const foodChainId = req.user.foodChain;

    // Check if order is currently being updated
    if (adminOrderUpdateLocks.has(orderId)) {
      return res.status(429).json({
        success: false,
        message: "Order is currently being updated. Please wait and try again.",
      });
    }

    // Lock the order for updates
    adminOrderUpdateLocks.set(orderId, Date.now());

    // Find the order
    const order = await Order.findOne({ _id: orderId, foodChainId });
    if (!order) {
      return res.status(404).json({
        success: false,
        message: "Order not found",
      });
    }

    // Check if order can be updated (only if payment is not completed)
    if (order.paymentStatus === "paid") {
      return res.status(400).json({
        success: false,
        message: "Cannot update order after payment is completed",
      });
    }

    // Validate items
    if (!items || !Array.isArray(items) || items.length === 0) {
      return res.status(400).json({
        success: false,
        message: "Items are required",
      });
    }

    // Calculate new total and validate items
    let totalAmount = 0;
    const validatedItems = [];

    for (const item of items) {
      if (!item.dishId || !item.quantity || !item.price) {
        return res.status(400).json({
          success: false,
          message: "Each item must have dishId, quantity, and price",
        });
      }

      // If dishName is not provided, fetch it from the database
      let dishName = item.dishName;
      if (!dishName) {
        const dish = await Dish.findById(item.dishId);
        if (dish) {
          dishName = dish.name;
        } else {
          // If dish is not found, use a fallback name
          dishName = "Unknown Dish";
        }
      }

      validatedItems.push({
        ...item,
        dishName,
      });

      totalAmount += item.price * item.quantity;
    }

    // Update order items while preserving served quantities
    order.items = validatedItems.map((item) => {
      // Find existing item to preserve served status
      const existingItem = order.items.find(
        (existing) => existing.dishId.toString() === item.dishId.toString()
      );

      const servedQuantity = existingItem
        ? existingItem.servedQuantity || 0
        : 0;
      const newQuantity = item.quantity;

      // If new quantity is less than served quantity, that's not allowed
      if (newQuantity < servedQuantity) {
        throw new Error(
          `Cannot reduce quantity below served amount for dish ${item.dishId}`
        );
      }

      return {
        dishId: item.dishId,
        quantity: newQuantity,
        dishName: item.dishName,
        price: item.price,
        isServed: servedQuantity > 0 && servedQuantity === newQuantity, // Fully served if all quantities are served
        servedQuantity: servedQuantity,
        servedAt: existingItem?.servedAt,
        servedBy: existingItem?.servedBy,
      };
    });
    order.totalAmount = totalAmount;
    order.modificationReason = "Items updated by admin";

    // Automatically reapply coupon if one exists, or apply new coupon if provided
    if (couponCode) {
      // If couponDiscount is provided, use it; otherwise recalculate
      if (couponDiscount !== undefined) {
        order.couponCode = couponCode;
        order.couponDiscount = couponDiscount;
        order.finalAmount = Math.max(0, totalAmount - couponDiscount);
      } else {
        // Set the coupon code and let reapplyCouponToOrder calculate the discount
        order.couponCode = couponCode;
        const couponResult = await reapplyCouponToOrder(order);
        if (!couponResult.success) {
          console.warn("Failed to apply coupon:", couponResult.message);
          order.couponCode = undefined;
          order.couponDiscount = 0;
          order.finalAmount = totalAmount;
        }
      }
    } else if (order.couponCode) {
      // Automatically reapply existing coupon with new total
      const couponResult = await reapplyCouponToOrder(order);
      if (!couponResult.success) {
        console.warn("Failed to reapply coupon:", couponResult.message);
      }
    } else {
      order.couponCode = undefined;
      order.couponDiscount = 0;
      order.finalAmount = totalAmount;
    }

    order.updatedAt = new Date();
    await order.save();

    // Update payment link if it exists and payment is not completed
    if (order.paymentStatus !== "paid") {
      try {
        const existingPayment = await Payment.findOne({ orderId });
        if (existingPayment && existingPayment.razorpayPaymentLinkId) {
          // Populate order with user information for payment link creation
          const populatedOrder = await Order.findById(orderId).populate(
            "userId",
            "name phone email"
          );

          const updatedPaymentLink = await updatePaymentLink(
            existingPayment.razorpayPaymentLinkId,
            populatedOrder || order
          );

          // Update payment record with new payment link details
          existingPayment.razorpayPaymentLinkId = updatedPaymentLink.id;
          existingPayment.paymentLink = updatedPaymentLink.short_url;
          existingPayment.amount = order.finalAmount || order.totalAmount;
          await existingPayment.save();
        }
      } catch (error) {
        console.error("Error updating payment link:", error);
        // Don't fail the order update if payment link update fails
      }
    }

    // Emit socket events for real-time notification
    await emitOrderStatusUpdate(orderId);
    await emitOrderItemsUpdate(orderId, "admin");

    // Release the lock
    adminOrderUpdateLocks.delete(orderId);

    res.status(200).json({
      success: true,
      message: "Order updated successfully",
      data: order,
    });
  } catch (error) {
    console.error(error);

    // Release the lock on error
    adminOrderUpdateLocks.delete(orderId);

    res.status(500).json({
      success: false,
      message: "Error updating order",
      error: error.message,
    });
  }
};
