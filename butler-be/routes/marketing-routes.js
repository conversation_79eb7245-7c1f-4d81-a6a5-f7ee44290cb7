import express from "express";
import {
  createCoupon,
  getAllCoupons,
  getCouponById,
  updateCoupon,
  deleteCoupon,
  validateCoupon,
  applyCoupon,
} from "../controllers/coupon-controller.js";
import {
  createOffer,
  getAllOffers,
  getOfferById,
  updateOffer,
  deleteOffer,
  getActiveOffers,
  getApplicableOffers,
} from "../controllers/offer-controller.js";
import {
  createCampaign,
  getAllCampaigns,
  getCampaignById,
  updateCampaign,
  deleteCampaign,
  sendCampaign,
  cancelCampaign,
  getCampaignRecipients,
  trackEmailOpen,
  trackEmailClick,
} from "../controllers/campaign-controller.js";
import { authenticateToken } from "../middlewares/auth.js";

const router = express.Router();

// Coupon routes
router.post("/admin/coupons", authenticateToken, createCoupon);
router.get("/admin/coupons", authenticateToken, getAllCoupons);
router.get("/admin/coupons/:id", authenticateToken, getCouponById);
router.put("/admin/coupons/:id", authenticateToken, updateCoupon);
router.delete("/admin/coupons/:id", authenticateToken, deleteCoupon);
router.post("/admin/coupons/validate", authenticateToken, validateCoupon);
router.post("/admin/coupons/apply", authenticateToken, applyCoupon);

// Offer routes
router.post("/admin/offers", authenticateToken, createOffer);
router.get("/admin/offers", authenticateToken, getAllOffers);
router.get("/admin/offers/:id", authenticateToken, getOfferById);
router.put("/admin/offers/:id", authenticateToken, updateOffer);
router.delete("/admin/offers/:id", authenticateToken, deleteOffer);

// Public offer routes (for customer app)
router.get("/offers", getActiveOffers);
router.post("/offers/applicable", getApplicableOffers);

// Campaign routes
router.post("/admin/campaigns", authenticateToken, createCampaign);
router.get("/admin/campaigns", authenticateToken, getAllCampaigns);
router.get("/admin/campaigns/:id", authenticateToken, getCampaignById);
router.put("/admin/campaigns/:id", authenticateToken, updateCampaign);
router.delete("/admin/campaigns/:id", authenticateToken, deleteCampaign);
router.post("/admin/campaigns/:id/send", authenticateToken, sendCampaign);
router.post("/admin/campaigns/:id/cancel", authenticateToken, cancelCampaign);
router.get(
  "/admin/campaigns/:id/recipients",
  authenticateToken,
  getCampaignRecipients
);

// Campaign tracking routes (public)
router.get("/track/open/:campaignId/:recipientId", trackEmailOpen);
router.get("/track/click/:campaignId/:recipientId", trackEmailClick);

export default router;
